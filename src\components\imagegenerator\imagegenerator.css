.imagegenerator{
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: auto;
    align-items: center;
    margin-top: 40px;
    margin-bottom: 20px;
    gap:20px;
}

.header{
    font-size: 50px;
    font-weight:500;
    padding-bottom: 30px;
    
}

.header span{
    color: pink;
}

.img-loading{
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}
.img-loading .image img {
    width: 400px;
    height: 400px;
    object-fit: cover;
    border: 2px solid black;
    border-radius: 15px;
    
}
.search-box {
  display: flex;
  width: 600px;
  height: 60px;
  border-radius: 30px;
  border: 2px solid #cccccc1b;
  align-items: center;
  padding: 0 20px;
  background-color: #76828e6b;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.search {
  width: 100%;
  height: 100%;
  background: transparent;
  border: none;
  font-size: 18px;
  outline: none;
  color: #ebe6e6;
  padding-left: 10px;
}

.search::placeholder {
  color: white;
}
.generatebutton {
 display: inline-block;  
  justify-content: center;
  align-items: center;
  width: 200px;                 /* Fixed: no space */
  height: 50px;
  border-radius: 50px;
  cursor: pointer;
  font-size: 20px;
  background: pink;
  color: black;                 /* Ensure text color is readable */
  border: none;
  outline: none;
  transition: transform 0.2s ease;
}

.generatebutton:hover {
  transform: translateX(10px);
}


