import React, { useState } from 'react';
import './imagegenerator.css';
import defaultimage from '../assets/defaultimage.png';
const imagegenerator = () => {

   const[image_url,setImage_url]=useState("/");
   let inputref=useref(null);

  const imagegenerator=async() =>{
    if (inputref.current.value===""){
        return 0;
    }
    const response = await fetch("https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg",
        {method:"POST",
        headers:{
            "Content-Type":"application/json",
            "Bearer https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg",
            "User-Agent":"Chrome",
        },
        body:JSON.stringify({
            prompt:`${inputref.current.value}`,
            n=1,
            size:"512x512",
        }),
    }
    );
    let data= await response.json();
    console.log(data);

}


    return (
        <div className='imagegenerator'>
           <div className="header">
           Ai Image <span>Generator</span>
           </div>
            <div className="img-loading">
                <div className="image"><img src={image_url==="/"?defaultimage:image_url} alt="" /></div>
                <div className="search-box">
                    <input type="text" ref={inputref} className='search' placeholder='Describe what you want to see' />
                        <button className="generatebutton" onClick={()=>{imagegenerator()}}>Generate</button>
                </div>
            </div>
        </div>
    )
}
export default imagegenerator;